#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试API修复的脚本
验证HTTP状态码处理和停止机制是否正常工作
"""

import sys
import time
import signal
import logging
from unittest.mock import Mock, patch

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_signal_handling():
    """测试信号处理功能"""
    logger.info("🧪 测试信号处理功能...")
    
    try:
        # 导入主模块
        import api_incremental_release_fetcher as fetcher
        
        # 检查全局变量是否存在
        assert hasattr(fetcher, 'stop_requested'), "缺少stop_requested全局变量"
        assert fetcher.stop_requested == False, "stop_requested初始值应为False"
        
        # 检查信号处理函数是否存在
        assert hasattr(fetcher, 'signal_handler'), "缺少signal_handler函数"
        
        # 模拟信号处理
        fetcher.signal_handler(signal.SIGINT, None)
        assert fetcher.stop_requested == True, "信号处理后stop_requested应为True"
        
        # 重置状态
        fetcher.stop_requested = False
        
        logger.info("✅ 信号处理功能测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 信号处理功能测试失败: {e}")
        return False

def test_http_status_handling():
    """测试HTTP状态码处理逻辑"""
    logger.info("🧪 测试HTTP状态码处理逻辑...")
    
    try:
        # 导入主模块
        import api_incremental_release_fetcher as fetcher
        
        # 创建模拟的API客户端
        client = fetcher.EnhancedDiscogsAPIClient(use_proxy=False)
        
        # 测试process_api_response函数
        mock_db = Mock()
        
        # 测试404处理
        result = fetcher.process_api_response(mock_db, 12345, None)
        logger.info(f"404处理结果: {result}")
        
        # 测试429处理
        result = fetcher.process_api_response(mock_db, 12345, "429")
        logger.info(f"429处理结果: {result}")
        
        # 测试成功处理
        mock_data = {"id": 12345, "title": "Test Release"}
        result = fetcher.process_api_response(mock_db, 12345, mock_data)
        logger.info(f"成功处理结果: {result}")
        
        # 测试失败处理
        result = fetcher.process_api_response(mock_db, 12345, False)
        logger.info(f"失败处理结果: {result}")
        
        logger.info("✅ HTTP状态码处理逻辑测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ HTTP状态码处理逻辑测试失败: {e}")
        return False

def test_get_release_return_values():
    """测试get_release方法的返回值逻辑"""
    logger.info("🧪 测试get_release方法返回值...")
    
    try:
        # 导入主模块
        import api_incremental_release_fetcher as fetcher
        
        # 检查get_release方法是否存在last_error_type逻辑
        import inspect
        source = inspect.getsource(fetcher.EnhancedDiscogsAPIClient.get_release)
        
        # 检查关键修改是否存在
        assert 'last_error_type' in source, "缺少last_error_type变量"
        assert 'last_error_type = "429"' in source, "缺少429错误类型设置"
        assert 'return "429"' in source, "缺少429返回值"
        assert 'stop_requested' in source, "缺少停止标志检查"
        
        logger.info("✅ get_release方法返回值逻辑检查通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ get_release方法返回值逻辑检查失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("🚀 开始API修复验证测试...")
    
    tests = [
        ("信号处理功能", test_signal_handling),
        ("HTTP状态码处理", test_http_status_handling),
        ("get_release返回值", test_get_release_return_values),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"测试: {test_name}")
        logger.info(f"{'='*50}")
        
        if test_func():
            passed += 1
        
        time.sleep(0.5)  # 短暂延迟
    
    logger.info(f"\n{'='*60}")
    logger.info(f"📊 测试结果: {passed}/{total} 通过")
    logger.info(f"{'='*60}")
    
    if passed == total:
        logger.info("🎉 所有测试通过！API修复验证成功")
        return True
    else:
        logger.error("❌ 部分测试失败，请检查修复代码")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试API修复的脚本
验证HTTP状态码处理和停止机制是否正常工作
"""

import sys
import time
import signal
import logging
from unittest.mock import Mock, patch

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_signal_handling():
    """测试信号处理功能"""
    logger.info("🧪 测试信号处理功能...")
    
    try:
        # 导入主模块
        import api_incremental_release_fetcher as fetcher
        
        # 检查全局变量是否存在
        assert hasattr(fetcher, 'stop_requested'), "缺少stop_requested全局变量"
        assert fetcher.stop_requested == False, "stop_requested初始值应为False"
        
        # 检查信号处理函数是否存在
        assert hasattr(fetcher, 'signal_handler'), "缺少signal_handler函数"
        
        # 模拟信号处理
        fetcher.signal_handler(signal.SIGINT, None)
        assert fetcher.stop_requested == True, "信号处理后stop_requested应为True"
        
        # 重置状态
        fetcher.stop_requested = False
        
        logger.info("✅ 信号处理功能测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 信号处理功能测试失败: {e}")
        return False

def test_http_status_handling():
    """测试HTTP状态码处理逻辑"""
    logger.info("🧪 测试HTTP状态码处理逻辑...")

    try:
        # 导入主模块
        import api_incremental_release_fetcher as fetcher

        # 创建模拟的API客户端
        client = fetcher.EnhancedDiscogsAPIClient(use_proxy=False)

        # 创建更完善的mock数据库
        mock_db = Mock()
        mock_collection = Mock()
        mock_db.__getitem__ = Mock(return_value=mock_collection)
        mock_collection.find_one = Mock(return_value=None)  # 假设记录不存在
        mock_collection.insert_one = Mock()

        # 测试404处理
        logger.info("测试404状态处理...")
        result = fetcher.process_api_response(mock_db, 12345, None)
        logger.info(f"404处理结果: {result}")
        assert result in ["404_inserted", "404_failed"], f"404处理结果异常: {result}"

        # 测试429处理
        logger.info("测试429状态处理...")
        result = fetcher.process_api_response(mock_db, 12345, "429")
        logger.info(f"429处理结果: {result}")
        assert result in ["429_inserted", "429_failed"], f"429处理结果异常: {result}"

        # 测试成功处理
        logger.info("测试成功数据处理...")
        mock_data = {"id": 12345, "title": "Test Release"}
        result = fetcher.process_api_response(mock_db, 12345, mock_data)
        logger.info(f"成功处理结果: {result}")
        assert result in ["success_inserted", "success_failed"], f"成功处理结果异常: {result}"

        # 测试失败处理
        logger.info("测试API失败处理...")
        result = fetcher.process_api_response(mock_db, 12345, False)
        logger.info(f"失败处理结果: {result}")
        assert result == "api_failed", f"失败处理结果异常: {result}"

        logger.info("✅ HTTP状态码处理逻辑测试通过")
        return True

    except Exception as e:
        logger.error(f"❌ HTTP状态码处理逻辑测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

def test_get_release_return_values():
    """测试get_release方法的返回值逻辑"""
    logger.info("🧪 测试get_release方法返回值...")

    try:
        # 导入主模块
        import api_incremental_release_fetcher as fetcher

        # 检查get_release方法是否存在last_error_type逻辑
        import inspect
        source = inspect.getsource(fetcher.EnhancedDiscogsAPIClient.get_release)

        # 检查关键修改是否存在
        assert 'last_error_type' in source, "缺少last_error_type变量"
        assert 'last_error_type = "429"' in source, "缺少429错误类型设置"
        assert 'return "429"' in source, "缺少429返回值"
        assert 'stop_requested' in source, "缺少停止标志检查"

        logger.info("✅ get_release方法返回值逻辑检查通过")
        return True

    except Exception as e:
        logger.error(f"❌ get_release方法返回值逻辑检查失败: {e}")
        return False

def test_429_status_flow():
    """专门测试429状态码的完整处理流程"""
    logger.info("🧪 测试429状态码完整处理流程...")

    try:
        # 导入主模块
        import api_incremental_release_fetcher as fetcher

        # 创建模拟数据库
        mock_db = Mock()
        mock_collection = Mock()
        mock_db.__getitem__ = Mock(return_value=mock_collection)
        mock_collection.find_one = Mock(return_value=None)
        mock_collection.insert_one = Mock()

        # 测试429状态码处理流程
        logger.info("1. 测试process_api_response对'429'字符串的处理...")
        result = fetcher.process_api_response(mock_db, 99999, "429")
        logger.info(f"   结果: {result}")

        # 验证是否调用了正确的插入函数
        expected_table = fetcher.COPY_TARGET_COLLECTION_NAME
        logger.info(f"2. 验证是否访问了正确的表: {expected_table}")
        mock_db.__getitem__.assert_called_with(expected_table)

        # 验证插入函数
        logger.info("3. 测试insert_429_record函数...")
        insert_result = fetcher.insert_429_record(mock_db, 99999)
        logger.info(f"   插入结果: {insert_result}")

        # 检查表名配置
        logger.info("4. 验证表名配置...")
        logger.info(f"   COPY_TARGET_COLLECTION_NAME = {fetcher.COPY_TARGET_COLLECTION_NAME}")
        logger.info(f"   NOT_FOUND_COLLECTION_NAME = {fetcher.NOT_FOUND_COLLECTION_NAME}")

        assert fetcher.COPY_TARGET_COLLECTION_NAME == 'release_copy', "429数据应插入release_copy表"
        assert fetcher.NOT_FOUND_COLLECTION_NAME == 'release_404', "404数据应插入release_404表"

        logger.info("✅ 429状态码完整处理流程测试通过")
        return True

    except Exception as e:
        logger.error(f"❌ 429状态码完整处理流程测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

def main():
    """主测试函数"""
    logger.info("🚀 开始API修复验证测试...")
    
    tests = [
        ("信号处理功能", test_signal_handling),
        ("HTTP状态码处理", test_http_status_handling),
        ("get_release返回值", test_get_release_return_values),
        ("429状态码完整流程", test_429_status_flow),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"测试: {test_name}")
        logger.info(f"{'='*50}")
        
        if test_func():
            passed += 1
        
        time.sleep(0.5)  # 短暂延迟
    
    logger.info(f"\n{'='*60}")
    logger.info(f"📊 测试结果: {passed}/{total} 通过")
    logger.info(f"{'='*60}")
    
    if passed == total:
        logger.info("🎉 所有测试通过！API修复验证成功")
        return True
    else:
        logger.error("❌ 部分测试失败，请检查修复代码")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

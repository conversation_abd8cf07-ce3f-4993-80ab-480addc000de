# Discogs API 数据获取器配置文件模板
# 复制此文件为 .env 并根据需要修改配置

# ================================
# 数据库配置
# ================================

# MongoDB连接URI
# 格式: mongodb://用户名:密码@主机:端口/数据库名
MONGO_URI=**********************************************************

# 数据库名称
DB_NAME=music_test

# ================================
# API配置
# ================================

# API基础URL (通常不需要修改)
API_BASE_URL=https://api.discogs.com/releases

# API请求间隔 (秒)
API_RATE_LIMIT=1.0

# API请求超时时间 (秒)
API_TIMEOUT=30

# 最大重试次数
MAX_RETRIES=3

# ================================
# 代理配置 (如需要)
# ================================

# HTTP代理
# HTTP_PROXY=http://127.0.0.1:7890

# HTTPS代理
# HTTPS_PROXY=http://127.0.0.1:7890

# ================================
# 运行配置
# ================================

# 测试模式记录数量
TEST_RECORDS_COUNT=10

# 日志级别 (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# 是否启用详细日志
VERBOSE_LOGGING=true

# ================================
# 数据表名配置
# ================================

# 成功数据表名
TARGET_COLLECTION_NAME=release_new

# 429限流数据表名
COPY_TARGET_COLLECTION_NAME=release_copy

# 404错误数据表名
NOT_FOUND_COLLECTION_NAME=release_404

# ================================
# 进度保存配置
# ================================

# 进度文件路径
PROGRESS_FILE=progress.json

# 进度保存间隔 (处理多少条记录后保存一次)
PROGRESS_SAVE_INTERVAL=10

# ================================
# 使用说明
# ================================

# 1. 复制此文件为 .env
# 2. 根据实际情况修改配置值
# 3. 取消注释需要的配置项 (删除行首的 #)
# 4. 保存文件后重新运行程序

# 示例：启用代理
# 取消注释以下两行并设置正确的代理地址
# HTTP_PROXY=http://127.0.0.1:7890
# HTTPS_PROXY=http://127.0.0.1:7890

# 示例：修改日志级别为DEBUG
# LOG_LEVEL=DEBUG

# 示例：增加测试记录数量
# TEST_RECORDS_COUNT=50

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
API优化功能测试脚本
测试新的浏览器模拟、智能重试和代理轮换功能
"""

import sys
import time
import logging
from api_incremental_release_fetcher import (
    BROWSER_IDENTITIES, 
    DISCOGS_ACCOUNTS, 
    PROXY_CONFIGS,
    calculate_backoff_delay,
    calculate_random_request_delay,
    ProxyManager,
    EnhancedDiscogsAPIClient
)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_browser_identities():
    """测试浏览器身份配置"""
    logger.info("🧪 测试浏览器身份配置...")
    
    print(f"浏览器身份数量: {len(BROWSER_IDENTITIES)}")
    for i, browser in enumerate(BROWSER_IDENTITIES):
        print(f"{i+1}. {browser.browser_name} on {browser.os_name}")
        print(f"   User-Agent: {browser.user_agent[:50]}...")
        print(f"   Accept: {browser.accept[:50]}...")
        print()
    
    # 测试账号分配
    print("账号浏览器身份分配:")
    for account in DISCOGS_ACCOUNTS:
        browser = account.browser_identity
        print(f"- {account.name}: {browser.browser_name} on {browser.os_name}")
    
    logger.info("✅ 浏览器身份配置测试完成")
    return True

def test_backoff_calculation():
    """测试退避算法"""
    logger.info("🧪 测试智能退避算法...")
    
    print("退避延迟计算测试:")
    for attempt in range(6):
        delay_with_jitter = calculate_backoff_delay(attempt, base_delay=1.0, max_delay=5.0, jitter=True)
        delay_without_jitter = calculate_backoff_delay(attempt, base_delay=1.0, max_delay=5.0, jitter=False)
        print(f"尝试 {attempt}: 无抖动={delay_without_jitter:.2f}s, 有抖动={delay_with_jitter:.2f}s")
    
    # 测试随机请求延迟
    print("\n随机请求延迟测试:")
    for i in range(5):
        delay = calculate_random_request_delay()
        print(f"随机延迟 {i+1}: {delay:.2f}s")
    
    logger.info("✅ 智能退避算法测试完成")
    return True

def test_proxy_manager():
    """测试代理管理器"""
    logger.info("🧪 测试代理管理器...")
    
    proxy_manager = ProxyManager(PROXY_CONFIGS)
    
    print(f"代理配置数量: {len(PROXY_CONFIGS)}")
    for proxy in PROXY_CONFIGS:
        print(f"- {proxy.name}: {proxy.http}")
    
    # 测试代理获取
    print("\n代理获取测试:")
    for i in range(3):
        proxy = proxy_manager.get_active_proxy()
        if proxy:
            print(f"获取代理 {i+1}: {proxy.name}")
        else:
            print(f"获取代理 {i+1}: None")
    
    # 测试代理失败记录
    print("\n代理失败记录测试:")
    proxy = proxy_manager.get_active_proxy()
    if proxy:
        print(f"测试代理: {proxy.name}")
        for i in range(4):
            proxy_manager.record_proxy_result(proxy, False)
            print(f"失败记录 {i+1}: 失败次数={proxy.failure_count}, 状态={'活跃' if proxy.is_active else '禁用'}")
    
    logger.info("✅ 代理管理器测试完成")
    return True

def test_api_client_initialization():
    """测试API客户端初始化"""
    logger.info("🧪 测试API客户端初始化...")
    
    try:
        # 测试启用代理的客户端
        client_with_proxy = EnhancedDiscogsAPIClient(use_proxy=True)
        print("✅ 启用代理的客户端初始化成功")
        
        # 测试禁用代理的客户端
        client_without_proxy = EnhancedDiscogsAPIClient(use_proxy=False)
        print("✅ 禁用代理的客户端初始化成功")
        
        # 检查账号管理器
        print(f"账号数量: {len(client_with_proxy.account_manager.accounts)}")
        
        # 检查代理管理器
        if client_with_proxy.proxy_manager:
            print(f"代理数量: {len(client_with_proxy.proxy_manager.proxy_configs)}")
        else:
            print("代理管理器: 未启用")
        
        logger.info("✅ API客户端初始化测试完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ API客户端初始化失败: {e}")
        return False

def test_session_creation():
    """测试会话创建"""
    logger.info("🧪 测试会话创建...")
    
    try:
        client = EnhancedDiscogsAPIClient(use_proxy=False)  # 禁用代理避免网络问题
        
        # 测试为每个账号创建会话
        for account in DISCOGS_ACCOUNTS:
            session = client._create_session(account)
            
            print(f"账号 {account.name} 会话创建成功:")
            print(f"  User-Agent: {session.headers.get('User-Agent', 'N/A')[:50]}...")
            print(f"  Accept: {session.headers.get('Accept', 'N/A')[:50]}...")
            print(f"  Authorization: {'已设置' if 'Authorization' in session.headers else '未设置'}")
            print()
        
        logger.info("✅ 会话创建测试完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 会话创建测试失败: {e}")
        return False

def run_all_tests():
    """运行所有测试"""
    logger.info("🚀 开始API优化功能测试...")
    print("=" * 60)
    
    tests = [
        ("浏览器身份配置", test_browser_identities),
        ("智能退避算法", test_backoff_calculation),
        ("代理管理器", test_proxy_manager),
        ("API客户端初始化", test_api_client_initialization),
        ("会话创建", test_session_creation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 测试: {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} - 通过")
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {e}")
    
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    print(f"通过: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        logger.info("🎉 所有测试通过！API优化功能正常")
        return True
    else:
        logger.error(f"❌ {total-passed} 个测试失败，请检查配置")
        return False

def main():
    """主函数"""
    print("API优化功能测试")
    print("=" * 60)
    
    success = run_all_tests()
    
    if success:
        print("\n💡 测试建议:")
        print("- 所有优化功能正常工作")
        print("- 可以开始使用优化后的API获取器")
        print("- 建议先在测试模式下运行验证效果")
        print("- 监控429错误频率和代理切换情况")
    else:
        print("\n⚠️  问题排查建议:")
        print("- 检查代理配置是否正确")
        print("- 验证网络连接状态")
        print("- 查看详细错误日志")
        print("- 确认所有依赖包已安装")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())

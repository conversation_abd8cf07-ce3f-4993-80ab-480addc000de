# Discogs API Release 数据获取器 - 独立运行指南

## 概述

本指南介绍如何在独立终端中运行 Discogs API Release 数据获取器。

## 快速开始

### Windows 系统

1. **双击运行启动脚本**
   ```
   start_api_fetcher.bat
   ```

2. **或者在命令提示符中运行**
   ```cmd
   cd /d "C:\path\to\discogs"
   start_api_fetcher.bat
   ```

### Linux/macOS 系统

1. **在终端中运行启动脚本**
   ```bash
   cd /path/to/discogs
   ./start_api_fetcher.sh
   ```

2. **或者直接运行Python脚本**
   ```bash
   python3 api_incremental_release_fetcher.py
   ```

## 运行模式

### 1. 测试模式（推荐首次使用）
- 处理少量数据（默认10条）
- 用于验证配置和功能
- 启动方式：选择菜单选项1或运行 `python api_incremental_release_fetcher.py`

### 2. 生产模式
- 处理所有数据
- 可能需要很长时间
- 启动方式：选择菜单选项2或运行 `python api_incremental_release_fetcher.py --production`

### 3. 自定义参数模式
- 可以指定各种参数
- 灵活配置运行方式

## 命令行参数

```bash
python api_incremental_release_fetcher.py [选项]

选项:
  --no-proxy        不使用代理
  --production      生产模式（处理所有数据）
  --start-id NUM    指定起始ID（覆盖数据库最大ID）
  --max-records NUM 最大处理记录数
  --help           显示帮助信息
```

### 使用示例

```bash
# 测试模式
python api_incremental_release_fetcher.py

# 生产模式
python api_incremental_release_fetcher.py --production

# 从指定ID开始处理
python api_incremental_release_fetcher.py --start-id 1000000

# 处理指定数量的记录
python api_incremental_release_fetcher.py --max-records 1000

# 不使用代理
python api_incremental_release_fetcher.py --no-proxy

# 组合参数
python api_incremental_release_fetcher.py --production --start-id 500000 --max-records 10000
```

## 环境检查

在运行主程序前，建议先运行环境检查：

```bash
python check_environment.py
```

这将检查：
- Python版本
- 必需的包
- 数据库连接
- 网络连接
- 文件权限
- 环境变量

## 配置文件

### 环境变量配置

1. 复制配置模板：
   ```bash
   cp config_template.env .env
   ```

2. 编辑 `.env` 文件，设置必要的配置：
   ```env
   MONGO_URI=mongodb://用户名:密码@主机:端口/数据库名
   DB_NAME=数据库名
   HTTP_PROXY=http://127.0.0.1:7890
   HTTPS_PROXY=http://127.0.0.1:7890
   ```

## 后台运行（Linux/macOS）

### 启动后台进程
```bash
# 使用启动脚本的后台模式
./start_api_fetcher.sh
# 选择选项4

# 或者直接使用nohup
nohup python3 api_incremental_release_fetcher.py --production > api_fetcher.log 2>&1 &
echo $! > api_fetcher.pid
```

### 查看后台进程状态
```bash
# 查看进程ID
cat api_fetcher.pid

# 查看进程是否运行
ps -p $(cat api_fetcher.pid)

# 查看日志
tail -f api_fetcher.log
```

### 停止后台进程
```bash
# 优雅停止
kill -TERM $(cat api_fetcher.pid)

# 强制停止
kill -KILL $(cat api_fetcher.pid)
```

## 日志和监控

### 日志文件
- 控制台输出：实时显示处理进度
- 日志文件：`api_fetcher.log`（后台运行时）

### 进度文件
- `progress.json`：保存当前处理进度
- 程序重启时会自动从上次位置继续

### 监控指标
- 处理速度（条/秒）
- 成功率
- 错误统计
- API账号状态

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查网络连接
   - 验证数据库配置
   - 确认数据库服务运行状态

2. **API连接失败**
   - 检查网络连接
   - 配置代理设置
   - 验证API密钥

3. **权限错误**
   - 确保有目录写权限
   - 检查文件权限设置

4. **内存不足**
   - 监控系统资源使用
   - 考虑分批处理

### 获取帮助

1. 运行环境检查：`python check_environment.py`
2. 查看帮助信息：`python api_incremental_release_fetcher.py --help`
3. 检查日志文件中的错误信息

## 性能优化建议

1. **网络优化**
   - 使用稳定的网络连接
   - 配置合适的代理

2. **数据库优化**
   - 确保数据库索引正确
   - 监控数据库性能

3. **系统资源**
   - 监控CPU和内存使用
   - 避免在高负载时运行

## 安全注意事项

1. **API密钥安全**
   - 不要在代码中硬编码API密钥
   - 使用环境变量或配置文件

2. **数据库安全**
   - 使用强密码
   - 限制数据库访问权限

3. **网络安全**
   - 使用安全的代理设置
   - 避免在不安全的网络环境中运行

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Discogs API 递增式Release数据获取器

功能：
1. 从数据库中查询当前最大ID
2. 从最大ID+1开始递增调用Discogs API
3. 支持代理配置以访问外网API
4. 处理不同响应状态：成功、404、429限流
5. 将数据分别存储到对应的数据库表中

特性：
- 内置代理配置支持翻墙访问
- 智能错误处理和状态记录
- API频率限制控制
- 测试模式支持
- 详细的日志记录

作者：AI Assistant
创建时间：2025-08-01
"""

import os
import sys
import time
import json
import requests
import logging
import argparse
import random
import signal
import math
from datetime import datetime, timezone
from pymongo import MongoClient
from typing import Dict, Optional, List
from dataclasses import dataclass
from enum import Enum

# 全局停止标志
stop_requested = False

def signal_handler(signum, frame):
    """信号处理函数，用于优雅停止程序"""
    global stop_requested
    signal_name = signal.Signals(signum).name
    logger.info(f"🛑 接收到停止信号 {signal_name}，正在优雅退出...")
    stop_requested = True

# 导入现有的数据转换函数
try:
    # 尝试导入数据转换函数
    import importlib.util
    spec = importlib.util.spec_from_file_location("api_release_converter", "api_release_补全器.py")
    if spec and spec.loader:
        converter_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(converter_module)
        convert_api_response_to_document = converter_module.convert_api_response_to_document
        CONVERT_FUNCTION_AVAILABLE = True
    else:
        raise ImportError("无法加载转换模块")
except ImportError:
    CONVERT_FUNCTION_AVAILABLE = False
    print("⚠️ 警告：无法导入convert_api_response_to_document函数，将使用简化版本")

# 配置参数
MONGO_URI = os.getenv('MONGO_URI', '**********************************************************')
DB_NAME = os.getenv('DB_NAME', 'music_test')
TARGET_COLLECTION_NAME = 'release_new'  # 存储成功数据和429状态
COPY_TARGET_COLLECTION_NAME = 'release_copy' 
NOT_FOUND_COLLECTION_NAME = 'release_404'  # 存储404错误

# API配置
API_BASE_URL = 'https://api.discogs.com/releases'
API_RATE_LIMIT = 1.0  # 1秒1次请求
API_TIMEOUT = 30  # 请求超时时间
MAX_RETRIES = 3  # 最大重试次数

# 代理配置类
@dataclass
class ProxyConfig:
    """代理配置类"""
    http: str
    https: str
    name: str
    is_active: bool = True
    failure_count: int = 0
    last_check_time: float = 0.0

# 多代理配置池
PROXY_CONFIGS = [
    ProxyConfig(
        http='http://127.0.0.1:7897',
        https='http://127.0.0.1:7897',
        name='proxy_7897'
    ),
    ProxyConfig(
        http='http://127.0.0.1:7890',
        https='http://127.0.0.1:7890',
        name='proxy_7890'
    ),
    ProxyConfig(
        http='http://127.0.0.1:1087',
        https='http://127.0.0.1:1087',
        name='proxy_1087'
    ),
]

# 默认代理配置（向后兼容）
PROXY_CONFIG = {
    'http': 'http://127.0.0.1:7897',
    'https': 'http://127.0.0.1:7897',
}

# 测试配置
TEST_MODE = True  # 默认测试模式，只处理20条数据
TEST_RECORDS_COUNT = 20

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('api_incremental_fetcher.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

# 账号状态枚举
class AccountStatus(Enum):
    ACTIVE = "active"
    RATE_LIMITED = "rate_limited"
    ERROR = "error"
    DISABLED = "disabled"

# 浏览器身份配置
@dataclass
class BrowserIdentity:
    """浏览器身份配置类"""
    user_agent: str
    accept: str
    accept_language: str
    accept_encoding: str
    sec_ch_ua: str
    sec_ch_ua_mobile: str
    sec_ch_ua_platform: str
    browser_name: str
    os_name: str

# 真实浏览器身份池
BROWSER_IDENTITIES = [
    # Chrome Windows
    BrowserIdentity(
        user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        accept="text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        accept_language="en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7",
        accept_encoding="gzip, deflate, br",
        sec_ch_ua='"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
        sec_ch_ua_mobile="?0",
        sec_ch_ua_platform='"Windows"',
        browser_name="Chrome",
        os_name="Windows"
    ),
    # Firefox Windows
    BrowserIdentity(
        user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/******** Firefox/121.0",
        accept="text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8",
        accept_language="en-US,en;q=0.5",
        accept_encoding="gzip, deflate, br",
        sec_ch_ua="",
        sec_ch_ua_mobile="",
        sec_ch_ua_platform="",
        browser_name="Firefox",
        os_name="Windows"
    ),
    # Safari macOS
    BrowserIdentity(
        user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15",
        accept="text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
        accept_language="en-US,en;q=0.9",
        accept_encoding="gzip, deflate, br",
        sec_ch_ua="",
        sec_ch_ua_mobile="",
        sec_ch_ua_platform="",
        browser_name="Safari",
        os_name="macOS"
    ),
    # Chrome macOS
    BrowserIdentity(
        user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        accept="text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        accept_language="en-US,en;q=0.9",
        accept_encoding="gzip, deflate, br",
        sec_ch_ua='"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
        sec_ch_ua_mobile="?0",
        sec_ch_ua_platform='"macOS"',
        browser_name="Chrome",
        os_name="macOS"
    ),
    # Edge Windows
    BrowserIdentity(
        user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0",
        accept="text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        accept_language="en-US,en;q=0.9",
        accept_encoding="gzip, deflate, br",
        sec_ch_ua='"Not_A Brand";v="8", "Chromium";v="120", "Microsoft Edge";v="120"',
        sec_ch_ua_mobile="?0",
        sec_ch_ua_platform='"Windows"',
        browser_name="Edge",
        os_name="Windows"
    ),
    # Firefox macOS
    BrowserIdentity(
        user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:121.0) Gecko/******** Firefox/121.0",
        accept="text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8",
        accept_language="en-US,en;q=0.5",
        accept_encoding="gzip, deflate, br",
        sec_ch_ua="",
        sec_ch_ua_mobile="",
        sec_ch_ua_platform="",
        browser_name="Firefox",
        os_name="macOS"
    )
]

# Discogs账号配置
@dataclass
class DiscogsAccount:
    """Discogs账号配置类"""
    user_agent: str
    user_token: str
    name: str
    browser_identity: BrowserIdentity
    status: AccountStatus = AccountStatus.ACTIVE
    last_request_time: float = 0.0
    request_count: int = 0
    error_count: int = 0
    rate_limit_until: float = 0.0
    success_count: int = 0
    consecutive_failures: int = 0  # 连续失败次数
    last_success_time: float = 0.0  # 最后成功时间

# 配置三个Discogs账号
DISCOGS_ACCOUNTS = [
    DiscogsAccount(
        user_agent="ywl-001/1.0 +https://github.com/ywl-001/discogs-fetcher",
        user_token="YQhzAXgybjisyugsFSaOqMHZWCgxeSxqXkWfkJKz",
        name="ywl-001",
        browser_identity=BROWSER_IDENTITIES[0]  # Chrome Windows
    ),
    DiscogsAccount(
        user_agent="ywl-003/1.0 +https://github.com/ywl-003/discogs-client",
        user_token="nGmlLrMQJCoMqPvjYjVWyzCcwAwSPSxsWKoCxOzb",
        name="ywl-003",
        browser_identity=BROWSER_IDENTITIES[1]  # Firefox Windows
    ),
    DiscogsAccount(
        user_agent="ywl-002/1.0 +https://github.com/ywl-002/discogs-fetcher",
        user_token="djmZkgDQOqMAlZfTBhQteMvMhQNITdWwYDSQXZFs",
        name="ywl-002",
        browser_identity=BROWSER_IDENTITIES[2]  # Safari macOS
    ),
]

# 智能重试策略工具函数
def calculate_backoff_delay(attempt: int, base_delay: float = 1.0, max_delay: float = 5.0, jitter: bool = True) -> float:
    """
    计算指数退避延迟时间

    Args:
        attempt: 重试次数（从0开始）
        base_delay: 基础延迟时间（秒）
        max_delay: 最大延迟时间（秒）
        jitter: 是否添加随机抖动

    Returns:
        延迟时间（秒）
    """
    # 指数退避：base_delay * (1.5 ^ attempt)
    delay = base_delay * (1.5 ** attempt)
    delay = min(delay, max_delay)

    if jitter:
        # 添加±20%的随机抖动
        jitter_range = delay * 0.2
        delay += random.uniform(-jitter_range, jitter_range)
        delay = max(0.1, delay)  # 确保最小延迟

    return delay

def calculate_random_request_delay(min_delay: float = 0.8, max_delay: float = 2.0) -> float:
    """
    计算随机请求延迟，模拟人类行为

    Args:
        min_delay: 最小延迟时间（秒）
        max_delay: 最大延迟时间（秒）

    Returns:
        随机延迟时间（秒）
    """
    return random.uniform(min_delay, max_delay)

class ProxyManager:
    """代理管理器"""

    def __init__(self, proxy_configs: List[ProxyConfig]):
        self.proxy_configs = proxy_configs
        self.current_proxy_index = 0
        self.max_failures = 3  # 最大失败次数

    def get_active_proxy(self) -> Optional[ProxyConfig]:
        """获取当前可用的代理"""
        if not self.proxy_configs:
            return None

        # 查找可用的代理
        for i in range(len(self.proxy_configs)):
            proxy = self.proxy_configs[(self.current_proxy_index + i) % len(self.proxy_configs)]
            if proxy.is_active and proxy.failure_count < self.max_failures:
                self.current_proxy_index = (self.current_proxy_index + i) % len(self.proxy_configs)
                return proxy

        # 如果没有可用代理，重置所有代理的失败计数
        logger.warning("⚠️ 所有代理都不可用，重置失败计数")
        for proxy in self.proxy_configs:
            proxy.failure_count = 0
            proxy.is_active = True

        return self.proxy_configs[0] if self.proxy_configs else None

    def record_proxy_result(self, proxy: ProxyConfig, success: bool):
        """记录代理使用结果"""
        if success:
            proxy.failure_count = 0
        else:
            proxy.failure_count += 1
            if proxy.failure_count >= self.max_failures:
                proxy.is_active = False
                logger.warning(f"⚠️ 代理 {proxy.name} 失败次数过多，暂时禁用")

    def switch_to_next_proxy(self):
        """切换到下一个代理"""
        self.current_proxy_index = (self.current_proxy_index + 1) % len(self.proxy_configs)
        logger.info(f"🔄 切换到代理: {self.proxy_configs[self.current_proxy_index].name}")

class DiscogsAccountManager:
    """Discogs账号管理器"""

    def __init__(self, accounts: List[DiscogsAccount]):
        self.accounts = accounts
        self.current_account_index = 0
        self.total_requests = 0
        self.total_success = 0
        self.total_errors = 0

    def get_active_account(self) -> Optional[DiscogsAccount]:
        """获取当前可用的账号"""
        current_time = time.time()

        # 检查当前账号是否可用
        current_account = self.accounts[self.current_account_index]
        if self._is_account_available(current_account, current_time):
            return current_account

        # 尝试切换到其他可用账号
        for i, account in enumerate(self.accounts):
            if i != self.current_account_index and self._is_account_available(account, current_time):
                logger.info(f"🔄 切换账号: {self.accounts[self.current_account_index].name} -> {account.name}")
                self.current_account_index = i
                return account

        # 如果所有账号都不可用，返回错误最少的账号
        best_account = min(self.accounts, key=lambda a: a.error_count)
        if best_account != current_account:
            logger.warning(f"⚠️ 所有账号都有问题，选择错误最少的账号: {best_account.name}")
            self.current_account_index = self.accounts.index(best_account)

        return best_account

    def _is_account_available(self, account: DiscogsAccount, current_time: float) -> bool:
        """检查账号是否可用"""
        # 检查是否被速率限制
        if current_time < account.rate_limit_until:
            return False

        # 检查账号状态
        if account.status in [AccountStatus.DISABLED, AccountStatus.ERROR]:
            return False

        # 检查错误率（如果错误率过高，暂时禁用）
        if account.request_count > 10 and account.error_count / account.request_count > 0.5:
            return False

        return True

    def record_request_result(self, account: DiscogsAccount, success: bool, status_code: Optional[int] = None):
        """记录请求结果"""
        account.request_count += 1
        self.total_requests += 1

        if success:
            account.success_count += 1
            account.error_count = max(0, account.error_count - 1)  # 成功时减少错误计数
            self.total_success += 1
            if account.status == AccountStatus.ERROR:
                account.status = AccountStatus.ACTIVE
                logger.info(f"✅ 账号 {account.name} 恢复正常")
        else:
            account.error_count += 1
            self.total_errors += 1

            if status_code == 429:
                account.status = AccountStatus.RATE_LIMITED
                account.rate_limit_until = time.time() + 60  # 1分钟后重试
                logger.warning(f"⏳ 账号 {account.name} 被限流，1分钟后重试")
            elif account.error_count >= 5:
                account.status = AccountStatus.ERROR
                logger.error(f"❌ 账号 {account.name} 错误过多，暂时禁用")

    def get_stats(self) -> Dict:
        """获取统计信息"""
        return {
            'total_requests': self.total_requests,
            'total_success': self.total_success,
            'total_errors': self.total_errors,
            'success_rate': self.total_success / max(1, self.total_requests),
            'accounts': [
                {
                    'name': acc.name,
                    'status': acc.status.value,
                    'requests': acc.request_count,
                    'success': acc.success_count,
                    'errors': acc.error_count,
                    'success_rate': acc.success_count / max(1, acc.request_count)
                }
                for acc in self.accounts
            ]
        }

class EnhancedDiscogsAPIClient:
    """增强的Discogs API客户端，支持账号轮换和智能重试"""

    def __init__(self, use_proxy=True):
        self.account_manager = DiscogsAccountManager(DISCOGS_ACCOUNTS)
        self.proxy_manager = ProxyManager(PROXY_CONFIGS) if use_proxy else None
        self.use_proxy = use_proxy
        self.base_delay = API_RATE_LIMIT
        self.max_delay = 5.0  # 降低最大延迟到5秒
        self.current_delay = self.base_delay

        logger.info(f"🚀 初始化增强API客户端，账号数量: {len(DISCOGS_ACCOUNTS)}")
        for account in DISCOGS_ACCOUNTS:
            browser = account.browser_identity
            logger.info(f"📱 账号: {account.name} ({browser.browser_name} on {browser.os_name})")

        if self.proxy_manager:
            logger.info(f"🌐 代理管理器已启用，代理数量: {len(PROXY_CONFIGS)}")
            for proxy in PROXY_CONFIGS:
                logger.info(f"🔗 代理: {proxy.name}")

    def _create_session(self, account: DiscogsAccount, proxy_config: Optional[ProxyConfig] = None) -> requests.Session:
        """为指定账号创建会话"""
        session = requests.Session()
        browser = account.browser_identity

        # 使用账号的浏览器身份设置完整的请求头
        headers = {
            'User-Agent': browser.user_agent,
            'Authorization': f'Discogs token={account.user_token}',
            'Accept': browser.accept,
            'Accept-Language': browser.accept_language,
            'Accept-Encoding': browser.accept_encoding,
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Cache-Control': 'max-age=0',
        }

        # 添加Chrome/Edge特有的Sec-CH-UA头
        if browser.sec_ch_ua:
            headers['Sec-CH-UA'] = browser.sec_ch_ua
            headers['Sec-CH-UA-Mobile'] = browser.sec_ch_ua_mobile
            headers['Sec-CH-UA-Platform'] = browser.sec_ch_ua_platform
            headers['Sec-Fetch-Dest'] = 'document'
            headers['Sec-Fetch-Mode'] = 'navigate'
            headers['Sec-Fetch-Site'] = 'none'
            headers['Sec-Fetch-User'] = '?1'

        # 随机决定是否使用原始User-Agent（10%概率）
        if random.random() < 0.1:
            headers['User-Agent'] = account.user_agent
            logger.debug(f"🔄 账号 {account.name} 使用原始User-Agent")

        session.headers.update(headers)

        # 设置代理
        if self.use_proxy and proxy_config:
            session.proxies.update({
                'http': proxy_config.http,
                'https': proxy_config.https
            })
            logger.debug(f"🌐 使用代理: {proxy_config.name}")

        return session
    
    def _wait_for_rate_limit(self, account: DiscogsAccount):
        """等待满足API频率限制"""
        current_time = time.time()
        elapsed = current_time - account.last_request_time

        if elapsed < self.current_delay:
            sleep_time = self.current_delay - elapsed
            logger.debug(f"⏱️ 频率控制：等待 {sleep_time:.2f} 秒 (账号: {account.name})")
            time.sleep(sleep_time)

    def _adjust_delay(self, success: bool):
        """根据请求结果调整延迟时间"""
        if success:
            # 成功时逐渐减少延迟
            self.current_delay = max(self.base_delay, self.current_delay * 0.95)
        else:
            # 失败时增加延迟
            self.current_delay = min(self.max_delay, self.current_delay * 1.5)

    def get_release(self, release_id: int) -> Optional[Dict]:
        """获取release数据，支持账号轮换和智能重试"""
        last_error_type = None  # 跟踪最后的错误类型

        for global_attempt in range(MAX_RETRIES * len(DISCOGS_ACCOUNTS)):
            # 检查停止标志
            if stop_requested:
                logger.info(f"🛑 检测到停止请求，中断API调用 - ID {release_id}")
                return False

            # 获取可用账号
            account = self.account_manager.get_active_account()
            if not account:
                logger.error("❌ 没有可用的账号")
                return False

            # 添加随机延迟，模拟人类行为
            human_delay = calculate_random_request_delay()
            time.sleep(human_delay)

            # 等待速率限制
            self._wait_for_rate_limit(account)

            # 获取代理配置
            proxy_config = None
            if self.proxy_manager:
                proxy_config = self.proxy_manager.get_active_proxy()

            # 创建会话
            session = self._create_session(account, proxy_config)
            url = f"{API_BASE_URL}/{release_id}"

            try:
                account.last_request_time = time.time()
                response = session.get(url, timeout=API_TIMEOUT)

                logger.info(f"🌐 API响应 - ID {release_id}: HTTP {response.status_code} (账号: {account.name})")

                # 记录代理使用结果
                if proxy_config:
                    self.proxy_manager.record_proxy_result(proxy_config, response.status_code < 400)

                if response.status_code == 200:
                    logger.info(f"✅ API成功 - ID {release_id} → 将返回JSON数据")
                    self.account_manager.record_request_result(account, True, 200)
                    self._adjust_delay(True)
                    return response.json()

                elif response.status_code == 404:
                    logger.info(f"⏭️ API 404 - ID {release_id} → 将返回None (插入release_404表)")
                    self.account_manager.record_request_result(account, True, 404)
                    return None  # ID不存在

                elif response.status_code == 429:
                    logger.warning(f"⏳ API限流 (429) - ID {release_id} → 设置last_error_type='429'")
                    self.account_manager.record_request_result(account, False, 429)
                    self._adjust_delay(False)
                    last_error_type = "429"  # 记录429错误类型
                    logger.info(f"🔄 已设置last_error_type='429' - ID {release_id}")

                    # 使用智能退避策略，最大等待5秒
                    wait_time = calculate_backoff_delay(global_attempt, base_delay=1.0, max_delay=5.0)
                    logger.info(f"⏳ 智能退避等待 {wait_time:.2f} 秒后重试...")
                    time.sleep(wait_time)

                    # 切换到下一个代理（如果有）
                    if self.proxy_manager:
                        self.proxy_manager.switch_to_next_proxy()

                    continue

                else:
                    logger.warning(f"⚠️ API请求失败 (ID: {release_id}): HTTP {response.status_code} (账号: {account.name})")
                    self.account_manager.record_request_result(account, False, response.status_code)
                    self._adjust_delay(False)

            except requests.exceptions.Timeout:
                logger.warning(f"⏰ API请求超时 (ID: {release_id}) (账号: {account.name})")
                self.account_manager.record_request_result(account, False)
                # 记录代理失败
                if proxy_config:
                    self.proxy_manager.record_proxy_result(proxy_config, False)

            except requests.exceptions.RequestException as e:
                logger.warning(f"🌐 网络错误 (ID: {release_id}): {e} (账号: {account.name})")
                self.account_manager.record_request_result(account, False)
                # 记录代理失败
                if proxy_config:
                    self.proxy_manager.record_proxy_result(proxy_config, False)

            # 重试前等待（智能指数退避）
            if global_attempt < MAX_RETRIES * len(DISCOGS_ACCOUNTS) - 1:
                wait_time = calculate_backoff_delay(global_attempt % 3, base_delay=0.5, max_delay=3.0)
                logger.debug(f"⏳ 重试前等待 {wait_time:.2f} 秒...")
                time.sleep(wait_time)

        # 根据最后的错误类型返回相应值
        if last_error_type == "429":
            logger.error(f"❌ API请求最终失败 (429限流) - ID {release_id} → 将返回'429'字符串 (插入release_copy表)")
            return "429"  # 返回429标识，用于正确分类到release_copy表
        else:
            logger.error(f"❌ API请求最终失败 - ID {release_id} → 将返回False (一般失败)")
            return False  # 表示一般请求失败

    def get_stats(self) -> Dict:
        """获取客户端统计信息"""
        stats = self.account_manager.get_stats()
        stats['current_delay'] = self.current_delay
        stats['base_delay'] = self.base_delay
        return stats

def connect_to_mongodb():
    """连接到MongoDB"""
    try:
        client = MongoClient(MONGO_URI)
        client.admin.command('ping')
        db = client[DB_NAME]

        # 确保目标集合存在
        if TARGET_COLLECTION_NAME not in db.list_collection_names():
            db.create_collection(TARGET_COLLECTION_NAME)
            logger.info(f"✅ 创建集合: {TARGET_COLLECTION_NAME}")

        # 确保404集合存在
        if NOT_FOUND_COLLECTION_NAME not in db.list_collection_names():
            db.create_collection(NOT_FOUND_COLLECTION_NAME)
            logger.info(f"✅ 创建集合: {NOT_FOUND_COLLECTION_NAME}")

        logger.info(f"✅ 成功连接到MongoDB")
        logger.info(f"📊 目标表: {DB_NAME}.{TARGET_COLLECTION_NAME}")
        logger.info(f"📊 404表: {DB_NAME}.{NOT_FOUND_COLLECTION_NAME}")
        return client, db
    except Exception as e:
        logger.error(f"❌ MongoDB连接失败: {e}")
        raise

def get_max_id_from_database(db) -> int:
    """从数据库中获取当前最大的ID值"""
    try:
        collection = db[TARGET_COLLECTION_NAME]

        # 查询最大ID
        result = collection.find({}, {'id': 1}).sort('id', -1).limit(1)
        max_doc = list(result)

        if max_doc:
            max_id = int(max_doc[0]['id'])
            logger.info(f"📊 数据库中当前最大ID: {max_id}")
            return max_id
        else:
            logger.info("📊 数据库为空，将从ID 1开始")
            return 0

    except Exception as e:
        logger.error(f"❌ 获取最大ID失败: {e}")
        return 0

def save_progress(current_id: int, stats: Dict, api_stats: Dict):
    """保存进度信息"""
    progress_data = {
        'current_id': current_id,
        'timestamp': datetime.now().isoformat(),
        'stats': stats,
        'api_stats': api_stats,
        'version': '2.0'  # 版本号，用于兼容性检查
    }

    try:
        with open('api_incremental_progress.json', 'w', encoding='utf-8') as f:
            json.dump(progress_data, f, indent=2, ensure_ascii=False)
        logger.debug(f"💾 进度已保存: ID {current_id}")
    except Exception as e:
        logger.error(f"❌ 保存进度失败: {e}")

def load_progress() -> Optional[Dict]:
    """加载进度信息"""
    try:
        if os.path.exists('api_incremental_progress.json'):
            with open('api_incremental_progress.json', 'r', encoding='utf-8') as f:
                progress_data = json.load(f)

            # 检查版本兼容性
            if progress_data.get('version') == '2.0':
                logger.info(f"📂 加载进度: ID {progress_data['current_id']}")
                return progress_data
            else:
                logger.warning("⚠️ 进度文件版本不兼容，将重新开始")

    except Exception as e:
        logger.error(f"❌ 加载进度失败: {e}")

    return None

def verify_resume_point(db, resume_id: int) -> bool:
    """验证断点续传的起始点是否有效"""
    try:
        # 检查resume_id-1是否存在于数据库中
        collection = db[TARGET_COLLECTION_NAME]
        prev_record = collection.find_one({'id': resume_id - 1})

        if prev_record:
            logger.info(f"✅ 断点续传验证通过: ID {resume_id-1} 存在于数据库")
            return True
        else:
            logger.warning(f"⚠️ 断点续传验证失败: ID {resume_id-1} 不存在于数据库")
            return False

    except Exception as e:
        logger.error(f"❌ 断点续传验证失败: {e}")
        return False

def insert_404_record(db, release_id: int) -> bool:
    """插入404错误记录到release_404表"""
    try:
        collection = db[NOT_FOUND_COLLECTION_NAME]
        logger.info(f"💾 准备插入404记录到表: {NOT_FOUND_COLLECTION_NAME} - ID {release_id}")

        # 检查是否已存在
        if collection.find_one({'id': release_id}):
            logger.info(f"📝 404 ID {release_id} 已存在于{NOT_FOUND_COLLECTION_NAME}表")
            return True

        # 插入新的404记录
        doc = {
            'id': release_id,
            'created_at': datetime.now(),
            'source': 'api_incremental_fetcher'
        }
        collection.insert_one(doc)
        logger.info(f"✅ 404记录已成功插入{NOT_FOUND_COLLECTION_NAME}表 - ID {release_id}")
        return True

    except Exception as e:
        logger.error(f"❌ 插入404记录到{NOT_FOUND_COLLECTION_NAME}表失败 (ID: {release_id}): {e}")
        return False

def insert_429_record(db, release_id: int) -> bool:
    """插入429限流记录到release_copy表"""
    try:
        collection = db[COPY_TARGET_COLLECTION_NAME]
        logger.info(f"💾 准备插入429记录到表: {COPY_TARGET_COLLECTION_NAME} - ID {release_id}")

        # 检查是否已存在
        if collection.find_one({'id': release_id}):
            logger.info(f"📝 429 ID {release_id} 已存在于{COPY_TARGET_COLLECTION_NAME}表")
            return True

        # 插入429状态记录
        doc = {
            'id': release_id,
            'status': 429,
            'created_at': datetime.now(),
            'source': 'api_incremental_fetcher'
        }
        collection.insert_one(doc)
        logger.info(f"✅ 429记录已成功插入{COPY_TARGET_COLLECTION_NAME}表 - ID {release_id}")
        return True

    except Exception as e:
        logger.error(f"❌ 插入429记录到{COPY_TARGET_COLLECTION_NAME}表失败 (ID: {release_id}): {e}")
        return False

def insert_success_record(db, api_data: Dict) -> bool:
    """插入成功获取的数据到release_new表"""
    try:
        collection = db[TARGET_COLLECTION_NAME]
        release_id = api_data.get('id')

        logger.info(f"🔍 准备插入数据 - ID {release_id}")

        # 检查是否已存在
        existing = collection.find_one({'id': release_id})
        if existing:
            logger.info(f"📝 成功数据 ID {release_id} 已存在于数据库")
            return True

        # 转换API数据为数据库文档格式
        if CONVERT_FUNCTION_AVAILABLE:
            try:
                doc = convert_api_response_to_document(api_data, db)
                if doc is None:
                    logger.warning(f"⚠️ 数据转换返回None - ID {release_id}，使用简化版本")
                    # 使用简化版本作为备选
                    doc = create_simple_document(api_data)
                else:
                    # 覆盖source字段以标识数据来源
                    doc['source'] = 'api_incremental_fetcher'
                    doc['updated_at'] = datetime.now()
            except Exception as e:
                logger.warning(f"⚠️ 使用现有转换函数失败 - ID {release_id}: {e}")
                # 使用简化版本作为备选
                doc = create_simple_document(api_data)
        else:
            # 简化版本的数据转换
            logger.info(f"🔧 使用简化版本转换数据 - ID {release_id}")
            doc = create_simple_document(api_data)

        logger.info(f"💾 正在插入文档 - ID {release_id}, 字段数: {len(doc)}")
        result = collection.insert_one(doc)
        logger.info(f"✅ 成功数据已插入 - ID {release_id}, MongoDB ID: {result.inserted_id}")
        return True

    except Exception as e:
        logger.error(f"❌ 插入成功数据失败 (ID: {api_data.get('id', 'unknown')}): {e}")
        import traceback
        logger.error(f"❌ 详细错误: {traceback.format_exc()}")
        return False

def create_simple_document(api_data: Dict) -> Dict:
    """创建简化的数据库文档"""
    return {
        'id': api_data.get('id'),
        'title': api_data.get('title', ''),
        'country': api_data.get('country', ''),
        'year': api_data.get('year'),
        'master_id': api_data.get('master_id'),
        'artists': api_data.get('artists', []),
        'labels': api_data.get('labels', []),
        'genres': api_data.get('genres', []),
        'images': api_data.get('images', []),
        'styles': api_data.get('styles', []),
        'formats': api_data.get('formats', []),
        'tracklist': api_data.get('tracklist', []),
        'identifiers': api_data.get('identifiers', []),
        'notes': api_data.get('notes', ''),
        'created_at': datetime.now(),
        'source': 'api_incremental_fetcher'
    }

def process_api_response(db, release_id: int, api_data) -> str:
    """处理API响应并存储到相应的数据库表"""
    logger.info(f"🔄 处理API响应 - ID {release_id}, 数据类型: {type(api_data)}, 数据值: {api_data}")

    if api_data is None:
        # 404错误
        logger.info(f"📝 检测到404状态 - ID {release_id} → 插入release_404表")
        if insert_404_record(db, release_id):
            logger.info(f"✅ 404数据已插入release_404表 - ID {release_id}")
            return "404_inserted"
        else:
            logger.error(f"❌ 404数据插入release_404表失败 - ID {release_id}")
            return "404_failed"
    elif api_data == "429":
        # 429限流
        logger.info(f"📝 检测到429状态 - ID {release_id} → 插入release_copy表")
        if insert_429_record(db, release_id):
            logger.info(f"✅ 429数据已插入release_copy表 - ID {release_id}")
            return "429_inserted"
        else:
            logger.error(f"❌ 429数据插入release_copy表失败 - ID {release_id}")
            return "429_failed"
    elif api_data is False:
        # API请求失败
        logger.info(f"📝 检测到API失败状态 - ID {release_id} → 不插入数据库")
        return "api_failed"
    elif isinstance(api_data, dict):
        # 成功获取数据
        logger.info(f"📝 检测到成功数据 - ID {release_id} → 插入release_new表")
        if insert_success_record(db, api_data):
            logger.info(f"✅ 成功数据已插入release_new表 - ID {release_id}")
            return "success_inserted"
        else:
            logger.error(f"❌ 成功数据插入release_new表失败 - ID {release_id}")
            return "success_failed"
    else:
        # 未知响应类型
        logger.warning(f"⚠️ 未知响应类型 - ID {release_id}: {type(api_data)}, 值: {api_data}")
        return "unknown_response"

def main():
    """主函数"""
    global stop_requested

    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    logger.info("🔧 已注册信号处理器 (SIGINT, SIGTERM)")

    parser = argparse.ArgumentParser(description='Discogs API 递增式Release数据获取器')
    parser.add_argument('--no-proxy', action='store_true', help='不使用代理')
    parser.add_argument('--production', action='store_true', help='生产模式（处理所有数据）')
    parser.add_argument('--start-id', type=int, help='指定起始ID（覆盖数据库最大ID）')
    parser.add_argument('--max-records', type=int, help='最大处理记录数')

    args = parser.parse_args()

    # 设置模式
    global TEST_MODE
    if args.production:
        TEST_MODE = False
        logger.info("🚀 生产模式：将处理所有数据")
    else:
        logger.info(f"🧪 测试模式：只处理 {TEST_RECORDS_COUNT} 条数据")

    try:
        # 连接数据库
        logger.info("🔗 连接数据库...")
        client, db = connect_to_mongodb()

        # 获取起始ID（支持断点续传）
        if args.start_id:
            start_id = args.start_id
            logger.info(f"📍 使用指定起始ID: {start_id}")
        else:
            # 尝试从进度文件恢复
            progress = load_progress()
            if progress and not TEST_MODE:
                resume_id = progress['current_id']
                if verify_resume_point(db, resume_id):
                    start_id = resume_id
                    logger.info(f"🔄 断点续传: 从ID {start_id} 继续")

                    # 显示之前的统计信息
                    prev_stats = progress.get('stats', {})
                    logger.info(f"📊 之前进度: 处理 {prev_stats.get('processed', 0)} 条，"
                              f"成功 {prev_stats.get('success', 0)} 条")
                else:
                    max_id = get_max_id_from_database(db)
                    start_id = max_id + 1
                    logger.info(f"📍 断点续传验证失败，从数据库最大ID+1开始: {start_id}")
            else:
                max_id = get_max_id_from_database(db)
                start_id = max_id + 1
                logger.info(f"📍 从数据库最大ID+1开始: {start_id}")

        # 创建API客户端
        logger.info("🌐 初始化增强API客户端...")
        api_client = EnhancedDiscogsAPIClient(use_proxy=not args.no_proxy)

        # 确定处理数量
        if args.max_records:
            max_records = args.max_records
        elif TEST_MODE:
            max_records = TEST_RECORDS_COUNT
        else:
            max_records = None  # 无限制

        # 开始处理
        logger.info("🚀 开始递增式API调用...")
        logger.info(f"📊 起始ID: {start_id}")
        if max_records:
            logger.info(f"📊 最大处理数量: {max_records}")
            logger.info(f"📊 结束ID: {start_id + max_records - 1}")

        # 统计信息
        stats = {
            'processed': 0,
            'success': 0,
            'not_found_404': 0,
            'rate_limited_429': 0,
            'api_failed': 0,
            'db_failed': 0
        }

        # 记录测试的ID范围
        test_ids = []

        current_id = start_id
        start_time = time.time()

        try:
            while True:
                # 检查停止标志
                if stop_requested:
                    logger.info("🛑 检测到停止请求，正在退出主循环...")
                    break

                # 检查是否达到最大处理数量
                if max_records and stats['processed'] >= max_records:
                    break

                # 记录测试ID
                if TEST_MODE:
                    test_ids.append(current_id)

                # 调用API
                logger.info(f"📞 调用API - ID {current_id}")
                api_data = api_client.get_release(current_id)
                logger.info(f"🔄 API调用完成 - ID {current_id}, 返回数据类型: {type(api_data)}")

                # 处理响应
                result = process_api_response(db, current_id, api_data)
                logger.info(f"📊 处理结果 - ID {current_id}: {result}")

                # 更新统计
                stats['processed'] += 1
                if result == "success_inserted":
                    stats['success'] += 1
                    logger.info(f"✅ 成功数据已统计 - ID {current_id} → release_new表")
                elif result == "404_inserted":
                    stats['not_found_404'] += 1
                    logger.info(f"⏭️ 404数据已统计 - ID {current_id} → release_404表")
                elif result == "429_inserted":
                    stats['rate_limited_429'] += 1
                    logger.info(f"⏳ 429数据已统计 - ID {current_id} → release_copy表")
                elif result == "api_failed":
                    stats['api_failed'] += 1
                    logger.info(f"❌ API失败已统计 - ID {current_id}")
                else:
                    stats['db_failed'] += 1
                    logger.info(f"💾 数据库失败已统计 - ID {current_id}")

                logger.info(f"📈 当前统计 - 成功:{stats['success']}, 404:{stats['not_found_404']}, 429:{stats['rate_limited_429']}, 失败:{stats['api_failed'] + stats['db_failed']}")

                # 显示进度和保存状态
                if stats['processed'] % 10 == 0 or TEST_MODE:
                    elapsed = time.time() - start_time
                    rate = stats['processed'] / elapsed if elapsed > 0 else 0

                    # 获取API统计信息
                    api_stats = api_client.get_stats()

                    logger.info(f"📊 进度: {stats['processed']}/{max_records or '∞'} "
                              f"(成功:{stats['success']}, 404:{stats['not_found_404']}, "
                              f"429:{stats['rate_limited_429']}, "
                              f"失败:{stats['api_failed'] + stats['db_failed']}) "
                              f"速率: {rate:.2f}/秒")

                    logger.info(f"🔄 API状态: 总请求 {api_stats['total_requests']}, "
                              f"成功率 {api_stats['success_rate']:.1%}, "
                              f"当前延迟 {api_stats['current_delay']:.1f}s")

                    # 保存进度
                    save_progress(current_id, stats, api_stats)

                current_id += 1

        except KeyboardInterrupt:
            logger.info("⏹️ 用户中断处理")
            stop_requested = True

        # 最终统计
        elapsed = time.time() - start_time
        final_api_stats = api_client.get_stats()

        logger.info("\n" + "=" * 60)
        logger.info("📊 处理完成统计")
        logger.info("=" * 60)
        logger.info(f"⏱️ 总耗时: {elapsed:.2f} 秒")
        logger.info(f"📈 处理速率: {stats['processed'] / elapsed:.2f} 条/秒")
        logger.info(f"📊 总处理数量: {stats['processed']}")
        logger.info(f"✅ 成功获取: {stats['success']}")
        logger.info(f"⏭️ 404错误: {stats['not_found_404']}")
        logger.info(f"⏳ 429限流: {stats['rate_limited_429']}")
        logger.info(f"❌ API失败: {stats['api_failed']}")
        logger.info(f"💾 数据库失败: {stats['db_failed']}")

        # API统计信息
        logger.info("\n" + "=" * 60)
        logger.info("🔄 API账号统计")
        logger.info("=" * 60)
        logger.info(f"📞 总API请求: {final_api_stats['total_requests']}")
        logger.info(f"✅ API成功率: {final_api_stats['success_rate']:.1%}")
        logger.info(f"⏱️ 最终延迟: {final_api_stats['current_delay']:.1f}s")

        for acc_stats in final_api_stats['accounts']:
            logger.info(f"👤 账号 {acc_stats['name']}: "
                      f"请求 {acc_stats['requests']}, "
                      f"成功率 {acc_stats['success_rate']:.1%}, "
                      f"状态 {acc_stats['status']}")

        # 显示测试ID范围
        if TEST_MODE and test_ids:
            logger.info(f"\n🧪 测试ID范围: {min(test_ids)} - {max(test_ids)}")
            logger.info(f"🧪 测试的具体ID: {test_ids}")

        # 保存最终进度
        save_progress(current_id - 1, stats, final_api_stats)

    except Exception as e:
        logger.error(f"❌ 程序执行失败: {e}")
        raise
    finally:
        if 'client' in locals():
            client.close()
            logger.info("🔗 数据库连接已关闭")

if __name__ == "__main__":
    main()
